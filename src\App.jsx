import React from 'react';

// Dummy data for demonstration
const differentialDiagnosis = [
  { label: "Granuloma Annulare", prob: 0.5 },
  { label: "Psoriasis", prob: 0.1 },
  { label: "Hypersensitivity Reaction", prob: 0.1 },
  { label: "Lichen Simplex Chronicus", prob: 0.1 },
  { label: "Lichen Planus", prob: 0.1 },
];

const App = () => {
  return (
    <div className="flex flex-col items-center p-6">
      {/* Header */}
      <h1 className="text-3xl font-bold mb-4">AI-Assisted Image Grading</h1>
      <p className="mb-2">Review the AI analysis and make adjustments as needed.</p>
      <p className="text-xs text-gray-500">AI Confidence: 80%</p>

      {/* Main content */}
      <div className="flex flex-row w-full mt-4">
        {/* Image Section */}
        <div className="flex-1 p-4">
          <div className="border rounded-md overflow-hidden">
            <img src="path/to/image.jpg" alt="Clinical" className="object-cover w-full h-64" />
          </div>
          <div className="flex justify-between mt-4">
            <button className="bg-blue-500 text-white px-4 py-2 rounded">Save Analysis</button>
            <button className="bg-yellow-500 text-white px-4 py-2 rounded">Flag for Review</button>
            <button className="bg-gray-300 text-black px-4 py-2 rounded">Skip Image</button>
            <button className="bg-green-500 text-white px-4 py-2 rounded">New Image</button>
          </div>
        </div>

        {/* Diagnosis Section */}
        <div className="flex-1 p-4 bg-gray-50 border rounded-md ml-4">
          <h2 className="text-xl font-semibold mb-4">Differential Diagnosis (DDx) Analysis</h2>
          {differentialDiagnosis.map((diagnosis, index) => (
            <div key={index} className="flex items-center mb-3">
              <select className="mr-2 border rounded-md p-1">
                <option>{diagnosis.label}</option>
                {/* Add more options if needed */}
              </select>
              <input type="number" min="0" max="1" step="0.1" value={diagnosis.prob} className="border rounded-md p-1 w-16" />
            </div>
          ))}
          <h2 className="mt-6 text-lg font-semibold">LLM Generated Recommendations</h2>
          <p className="mt-2">
            {"\"response\": \"1. Perform a punch or shave biopsy of the lesion to confirm diagnosis and rule out malignancy. Consider KOH prep or skin scraping if fungal infection is suspected. 3. Order basic blood work (CBC, ESR, ANA) if systemic disease or autoimmune etiology is suspected.\""}
          </p>
        </div>
      </div>
    </div>
  );
};

export default App;
