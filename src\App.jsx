import React, { useState } from 'react';

// Enhanced medical data with more realistic probabilities and additional metadata
const differentialDiagnosis = [
  {
    id: 1,
    label: "Granuloma Annulare",
    prob: 0.5,
    confidence: "High",
    description: "Ring-shaped skin lesion with characteristic histology",
    icd10: "L92.0"
  },
  {
    id: 2,
    label: "Psoriasis",
    prob: 0.1,
    confidence: "Low",
    description: "Chronic inflammatory skin condition",
    icd10: "L40.9"
  },
  {
    id: 3,
    label: "Hypersensitivity Reaction",
    prob: 0.1,
    confidence: "Medium",
    description: "Allergic or contact dermatitis response",
    icd10: "L23.9"
  },
  {
    id: 4,
    label: "Lichen Simplex Chronicus",
    prob: 0.1,
    confidence: "Low",
    description: "Chronic scratching-induced skin thickening",
    icd10: "L28.0"
  },
  {
    id: 5,
    label: "Lichen Planus",
    prob: 0.1,
    confidence: "Low",
    description: "Inflammatory condition affecting skin and mucous membranes",
    icd10: "L43.9"
  },
];

const App = () => {
  const [aiConfidence] = useState(85);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simplified Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">AI-Assisted Image Grading</h1>
            <p className="text-sm text-gray-600 mt-1">Review the AI analysis and make adjustments as needed.</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
              AI Confidence: {aiConfidence}%
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Clinical Image Section */}
          <div className="bg-white rounded-lg shadow border border-gray-200">
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">Clinical Image</h2>
            </div>

            <div className="p-4">
              {/* Medical Image Display */}
              <div className="relative bg-gray-100 rounded-lg overflow-hidden" style={{ aspectRatio: '4/3' }}>
                <img
                  src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Clinical skin lesion"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-3 right-3 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                  High Resolution
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-4 grid grid-cols-2 gap-3">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200 flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                  </svg>
                  <span>SAVE ANALYSIS</span>
                </button>
                <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200 flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
                  </svg>
                  <span>FLAG FOR REVIEW</span>
                </button>
                <button className="bg-gray-400 hover:bg-gray-500 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200 flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>SKIP IMAGE</span>
                </button>
                <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200 flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>NEW IMAGE</span>
                </button>
              </div>
            </div>
          </div>

          {/* Differential Diagnosis Section */}
          <div className="bg-white rounded-lg shadow border border-gray-200">
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Differential Diagnosis (DDx) Analysis</h2>
                <span className="text-sm text-gray-500">Review and edit the AI-suggested diagnoses. The most probable cannot exceed 1.0</span>
              </div>
            </div>

            <div className="p-4">
              {/* Diagnosis List */}
              <div className="space-y-3">
                {differentialDiagnosis.map((diagnosis) => (
                  <div key={diagnosis.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Select Diagnosis:
                      </label>
                      <select
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={diagnosis.label}
                        onChange={() => {}}
                      >
                        <option value={diagnosis.label}>{diagnosis.label}</option>
                        <option value="Psoriasis">Psoriasis</option>
                        <option value="Eczema">Eczema</option>
                        <option value="Contact Dermatitis">Contact Dermatitis</option>
                        <option value="Seborrheic Dermatitis">Seborrheic Dermatitis</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">{diagnosis.description}</p>
                    </div>
                    <div className="w-20">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Prob:
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="1"
                        step="0.1"
                        value={diagnosis.prob}
                        className="w-full px-2 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        onChange={() => {}}
                      />
                    </div>
                  </div>
                ))}
              </div>

              {/* LLM Generated Recommendations */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">LLM Generated Recommendations</h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="text-sm text-gray-800 leading-relaxed">
                    <p className="mb-3">
                      <span className="font-semibold">1.</span> Perform a punch or shave biopsy of the lesion to confirm diagnosis and rule out malignancy.
                    </p>
                    <p className="mb-3">
                      <span className="font-semibold">2.</span> Consider KOH prep or skin scraping if fungal infection is suspected.
                    </p>
                    <p>
                      <span className="font-semibold">3.</span> Order basic blood work (CBC, ESR, ANA) if systemic disease or autoimmune etiology is suspected.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default App;
